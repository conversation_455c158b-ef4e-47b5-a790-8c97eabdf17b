# Find Catch2 package
find_package(Catch2 3 QUIET)

if(NOT Catch2_FOUND)
  # If Catch2 is not found, try to fetch it
  include(<PERSON><PERSON><PERSON>ontent)
  FetchContent_Declare(
    Catch2
    GIT_REPOSITORY https://github.com/catchorg/Catch2.git
    GIT_TAG        v3.8.1
  )
  FetchContent_MakeAvailable(Catch2)
endif()

# Create test executable
add_executable(cmake_template_tests test_main.cpp)

# Link libraries
target_link_libraries(cmake_template_tests
  PRIVATE
    cmake_template::cmake_template_options
    cmake_template::cmake_template_warnings
    Catch2::Catch2WithMain
)

# Include Catch2 extras for test discovery
if(TARGET Catch2::Catch2)
  include(${Catch2_SOURCE_DIR}/extras/Catch.cmake)
  catch_discover_tests(cmake_template_tests)
endif()
