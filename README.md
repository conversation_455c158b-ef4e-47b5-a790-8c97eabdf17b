# cmake_template

[![ci](https://github.com/DjXray30/cmake_template/actions/workflows/ci.yml/badge.svg)](https://github.com/DjXray30/cmake_template/actions/workflows/ci.yml)
[![codecov](https://codecov.io/gh/DjXray30/cmake_template/branch/main/graph/badge.svg)](https://codecov.io/gh/DjXray30/cmake_template)
[![CodeQL](https://github.com/DjXray30/cmake_template/actions/workflows/codeql-analysis.yml/badge.svg)](https://github.com/DjXray30/cmake_template/actions/workflows/codeql-analysis.yml)

## About cmake_template



## More Details

 * [Dependency Setup](README_dependencies.md)
 * [Building Details](README_building.md)
 * [Troubleshooting](README_troubleshooting.md)
 * [Docker](README_docker.md)
