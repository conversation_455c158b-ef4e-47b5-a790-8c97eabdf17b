# cmake_template

A minimal, clean C++ CMake template suitable for both small test projects and larger applications.

## About

This template provides a streamlined starting point for C++ projects with:

-   **C++23** as the default standard
-   Clean, organized directory structure
-   Basic compiler warnings enabled
-   Optional testing with Catch2
-   Scalable architecture for growing projects
-   No unnecessary complexity or advanced features

## Project Structure

```
cmake_template/
├── CMakeLists.txt          # Main CMake configuration
├── README.md               # This file
├── src/
│   ├── CMakeLists.txt      # Source build configuration
│   └── main.cpp            # Main application source
├── include/
│   └── cmake_template/     # Public headers (if needed)
└── test/
    ├── CMakeLists.txt      # Test build configuration
    └── test_main.cpp       # Basic tests
```

## Building

```bash
# Create build directory
mkdir build && cd build

# Configure
cmake ..

# Build
cmake --build .

# Run the application
./src/cmake_template_app

# Run tests (optional)
ctest
```

## Usage

This template is designed to be:

-   **Simple**: Start with a basic "Hello World" and expand as needed
-   **Scalable**: Add more source files, libraries, and dependencies easily
-   **Modern**: Uses C++23 and modern CMake practices
-   **Clean**: No unnecessary complexity or advanced features you don't need

## Customization

-   Change the project name in the main `CMakeLists.txt`
-   Add source files to the `src/` directory
-   Add headers to the `include/cmake_template/` directory
-   Add tests to the `test/` directory
-   Add dependencies using `find_package()` or `FetchContent`
