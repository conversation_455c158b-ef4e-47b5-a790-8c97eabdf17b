{"version": 3, "configurePresets": [{"name": "default", "displayName": "<PERSON><PERSON><PERSON>g", "description": "Default build using Ninja generator", "generator": "Ninja", "binaryDir": "${sourceDir}/build/${presetName}", "cacheVariables": {"CMAKE_BUILD_TYPE": "Debug", "CMAKE_EXPORT_COMPILE_COMMANDS": "ON"}}, {"name": "debug", "displayName": "Debug", "description": "Debug build with all debugging features", "inherits": "default", "cacheVariables": {"CMAKE_BUILD_TYPE": "Debug"}}, {"name": "release", "displayName": "Release", "description": "Release build optimized for performance", "inherits": "default", "cacheVariables": {"CMAKE_BUILD_TYPE": "Release"}}, {"name": "relwithdebinfo", "displayName": "RelWithDebInfo", "description": "Release build with debug information", "inherits": "default", "cacheVariables": {"CMAKE_BUILD_TYPE": "RelWithDebInfo"}}], "buildPresets": [{"name": "default", "configurePreset": "default"}, {"name": "debug", "configurePreset": "debug"}, {"name": "release", "configurePreset": "release"}, {"name": "relwithdebinfo", "configurePreset": "relwithdebinfo"}], "testPresets": [{"name": "default", "configurePreset": "default", "output": {"outputOnFailure": true}}, {"name": "debug", "configurePreset": "debug", "output": {"outputOnFailure": true}}, {"name": "release", "configurePreset": "release", "output": {"outputOnFailure": true}}]}