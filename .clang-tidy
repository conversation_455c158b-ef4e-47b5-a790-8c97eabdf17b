---
Checks: "*,
        -abseil-*,
        -altera-*,
        -android-*,
        -fuchsia-*,
        -google-*,
        -llvm*,
        -modernize-use-trailing-return-type,
        -zircon-*,
        -readability-else-after-return,
        -readability-static-accessed-through-instance,
        -readability-avoid-const-params-in-decls,
        -cppcoreguidelines-non-private-member-variables-in-classes,
        -misc-non-private-member-variables-in-classes,
        -misc-no-recursion,
        -misc-use-anonymous-namespace,
        -misc-use-internal-linkage
"
WarningsAsErrors: ''
HeaderFilterRegex: ''
FormatStyle:     none

CheckOptions:
  - key: readability-identifier-length.IgnoredVariableNames
    value: 'x|y|z'
  - key: readability-identifier-length.IgnoredParameterNames
    value: 'x|y|z'





